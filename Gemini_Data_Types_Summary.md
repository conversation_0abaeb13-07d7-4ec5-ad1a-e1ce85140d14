# Gemini Model Data Types Summary
## Quick Reference Guide for Heuristic Evaluation System

---

## 🎯 Overview

Your heuristic evaluation script provides **three main types of data** to the Gemini model for comprehensive UI evaluation:

---

## 📊 Data Types Breakdown

### 1. 🖼️ **Visual Data (Images)**

**What is sent:**
- **Full Screenshot**: Complete interface with target element highlighted in red rectangle
- **Element Close-up**: Cropped view showing only the specific UI element being evaluated

**Technical Details:**
- **Format**: PNG images encoded as Base64 strings
- **Size Limit**: Maximum 1024x1024 pixels (automatically resized)
- **Quality**: High quality with LANCZOS resampling
- **Transmission**: Sent as part of multimodal content to Gemini Vision API

**Code Example:**
```python
content_parts = [
    prompt_text,                                    # Text instructions
    images_data['full_screenshot']['image'],        # Full interface image
    "📸 FULL SCREENSHOT: Complete interface...",    # Image description
    images_data['element_closeup']['image'],        # Element close-up
    "🔍 ELEMENT CLOSE-UP: Detailed view..."        # Image description
]
```

---

### 2. 📋 **Structured JSON Data**

**What is sent:**
- Complete technical specifications of the UI element in JSON format

**Data Structure:**
```json
{
    "index": 0,                           // Element index in dataset
    "label": "Button",                    // Human-readable label
    "coordinates": {                      // Position and size
        "x": 100,
        "y": 200,
        "width": 150,
        "height": 40
    },
    "tag": "button",                      // HTML tag name
    "text": "Submit",                      // Text content
    "css_selector": ".submit-btn",        // CSS selector
    "xpath": "//button[@class='submit-btn']", // XPath
    "computed_style": {                   // CSS properties
        "background-color": "#007bff",
        "font-size": "14px",
        "border-radius": "4px"
    },
    "attributes": {                       // HTML attributes
        "id": "submit-button",
        "class": "submit-btn primary",
        "aria-label": "Submit form"
    }
}
```

**Enhanced Context (for section-wise evaluation):**
```json
{
    // Basic element info (as above) +
    "parent_section_context": {          // Section relationship
        "section_label": "Form Section",
        "section_coordinates": {...},
        "section_key": "form_1"
    },
    "child_elements": [...],             // Child element info
    "is_child_element": true,            // Hierarchy flag
    "has_visual_context": true,          // Visual data available
    "evaluation_level": 1                // Evaluation hierarchy level
}
```

---

### 3. 📝 **Text Prompts & Instructions**

**What is sent:**
- Detailed evaluation instructions combining heuristic principles with element-specific guidance

**Components:**

#### A. **Nielsen's 10 Usability Heuristics**
Complete descriptions of all 10 principles:
1. Visibility of System Status
2. Match Between System and Real World
3. User Control and Freedom
4. Consistency and Standards
5. Error Prevention
6. Recognition Rather Than Recall
7. Flexibility and Efficiency of Use
8. Aesthetic and Minimalist Design
9. Help Users Recognize, Diagnose, and Recover from Errors
10. Help and Documentation

#### B. **Evaluation Instructions**
```
You are a UX expert conducting a comprehensive heuristic evaluation of a UI element.

HEURISTIC EVALUATION PRINCIPLES:
[Detailed descriptions of Nielsen's 10 heuristics]

ELEMENT TO EVALUATE:
[JSON element data]

EVALUATION TASK:
Analyze this UI element against ALL 10 Nielsen's usability heuristics...
```

#### C. **Visual Analysis Instructions** (when images are provided)
```
🖼️ VISUAL CONTEXT: You have been provided with actual screenshots.

📸 IMAGE ANALYSIS INSTRUCTIONS:
- The first image shows the full interface with the element highlighted in red
- The second image shows a close-up view of the specific element
- Analyze visual hierarchy, contrast, spacing, typography, colors
- Consider accessibility aspects (contrast ratios, text size)
```

---

## 🔄 **Data Flow Process**

### Standard Evaluation:
1. **Prepare Element Data** → JSON structure with technical details
2. **Create Prompt** → Text instructions with heuristic principles
3. **Send to Gemini** → Text-only API call
4. **Parse Response** → Structured evaluation results

### Visual-Enhanced Evaluation:
1. **Prepare Element Data** → JSON structure + coordinates
2. **Process Screenshots** → Highlight element + create close-up
3. **Encode Images** → Base64 PNG format
4. **Create Multimodal Content** → Combine text + images
5. **Send to Gemini Vision** → Multimodal API call
6. **Parse Response** → Enhanced evaluation with visual insights

---

## 🎛️ **Configuration Settings**

```python
# Model Configuration
MODEL_NAME = "gemini-1.5-pro-latest"  # Vision-capable model
TEMPERATURE = 0.7                     # Response creativity
MAX_TOKENS = 10000                    # Response length limit

# Vision Configuration  
ENABLE_VISION = True                  # Enable visual analysis
IMAGE_QUALITY = "high"                # Image quality level
MAX_IMAGE_SIZE = (1024, 1024)         # Size constraints
```

---

## 📤 **Expected Response Format**

Gemini returns structured JSON with:

```json
{
    "violations": [
        {
            "heuristic": "Consistency and Standards",
            "severity": "medium",
            "description": "Button styling inconsistent with other primary buttons",
            "recommendation": "Apply consistent primary button styling"
        }
    ],
    "passed_checks": [
        {
            "heuristic": "Visibility of System Status",
            "reason": "Button clearly indicates its function through text and styling"
        }
    ],
    "overall_score": 85,
    "key_recommendations": ["Improve consistency", "Enhance accessibility"],
    "summary": "Generally well-designed button with minor consistency issues"
}
```

---

## 🔧 **Key Features**

- **Multimodal Analysis**: Combines visual and technical data for comprehensive evaluation
- **Hierarchical Evaluation**: Supports section-wise analysis with parent-child relationships
- **Fallback Mechanisms**: Graceful degradation when visual data is unavailable
- **Structured Output**: Consistent JSON format for easy processing
- **Error Handling**: Robust error handling with diagnostic information

---

## 💡 **Benefits**

✅ **Enhanced Accuracy**: Visual context improves evaluation precision  
✅ **Comprehensive Analysis**: Technical + visual + heuristic evaluation  
✅ **Scalable**: Automated evaluation for large UI datasets  
✅ **Actionable**: Specific recommendations for improvements  
✅ **Consistent**: Standardized evaluation criteria across all elements  

---

This summary provides a complete overview of the data types and processing pipeline used in your Gemini-powered heuristic evaluation system.
