#!/usr/bin/env python3
"""
PDF Documentation Generator for Gemini Data Integration
Creates a professional PDF document from the markdown documentation
"""

from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Table, TableStyle
from reportlab.lib.colors import HexColor, black, white, blue, red, green
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
from reportlab.pdfgen import canvas
from datetime import datetime
import os

class PDFDocumentGenerator:
    def __init__(self, filename="Gemini_Data_Integration_Documentation.pdf"):
        self.filename = filename
        self.doc = SimpleDocTemplate(
            filename,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        self.styles = getSampleStyleSheet()
        self.story = []
        self._setup_custom_styles()

    def _setup_custom_styles(self):
        """Setup custom paragraph styles for the document"""
        # Title style
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=HexColor('#1f4e79')
        )

        # Subtitle style
        self.subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=HexColor('#2e75b6')
        )

        # Section header style
        self.section_style = ParagraphStyle(
            'SectionHeader',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceBefore=20,
            spaceAfter=12,
            textColor=HexColor('#1f4e79'),
            borderWidth=1,
            borderColor=HexColor('#1f4e79'),
            borderPadding=5
        )

        # Subsection header style
        self.subsection_style = ParagraphStyle(
            'SubsectionHeader',
            parent=self.styles['Heading3'],
            fontSize=14,
            spaceBefore=15,
            spaceAfter=8,
            textColor=HexColor('#2e75b6')
        )

        # Code style
        self.code_style = ParagraphStyle(
            'CodeStyle',
            parent=self.styles['Code'],
            fontSize=10,
            fontName='Courier',
            backgroundColor=HexColor('#f5f5f5'),
            borderWidth=1,
            borderColor=HexColor('#cccccc'),
            borderPadding=8,
            leftIndent=20,
            rightIndent=20
        )

        # Body text style
        self.body_style = ParagraphStyle(
            'BodyText',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=8,
            alignment=TA_JUSTIFY,
            textColor=black
        )

        # List item style
        self.list_style = ParagraphStyle(
            'ListItem',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=4,
            leftIndent=20,
            bulletIndent=10
        )

    def add_title_page(self):
        """Add title page to the document"""
        # Main title
        title = Paragraph("Gemini Model Data Integration Documentation", self.title_style)
        self.story.append(title)
        self.story.append(Spacer(1, 0.3*inch))

        # Subtitle
        subtitle = Paragraph("Heuristic Evaluation System - Complete Technical Specification", self.subtitle_style)
        self.story.append(subtitle)
        self.story.append(Spacer(1, 0.5*inch))

        # Document info
        info_data = [
            ["Document Type:", "Technical Specification"],
            ["System:", "UI Heuristic Evaluation with Gemini AI"],
            ["Model:", "Google Gemini 1.5 Pro (Vision-capable)"],
            ["Evaluation Framework:", "Nielsen's 10 Usability Heuristics"],
            ["Date:", datetime.now().strftime("%B %d, %Y")],
            ["Version:", "1.0"]
        ]

        info_table = Table(info_data, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), HexColor('#f0f0f0')),
            ('TEXTCOLOR', (0, 0), (-1, -1), black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#cccccc'))
        ]))

        self.story.append(info_table)
        self.story.append(PageBreak())

    def add_table_of_contents(self):
        """Add table of contents"""
        toc_title = Paragraph("Table of Contents", self.section_style)
        self.story.append(toc_title)
        self.story.append(Spacer(1, 0.2*inch))

        toc_items = [
            "1. System Overview",
            "2. Data Types Provided to Gemini",
            "3. Configuration Settings",
            "4. Visual Data Processing",
            "5. JSON Data Structure",
            "6. Prompt Engineering",
            "7. Multimodal Content Integration",
            "8. Response Processing",
            "9. Evaluation Workflows",
            "10. Technical Implementation Details"
        ]

        for item in toc_items:
            toc_para = Paragraph(f"• {item}", self.list_style)
            self.story.append(toc_para)

        self.story.append(PageBreak())

    def add_system_overview(self):
        """Add system overview section"""
        self.story.append(Paragraph("1. System Overview", self.section_style))

        # Purpose
        self.story.append(Paragraph("Purpose", self.subsection_style))
        purpose_text = """The Heuristic Evaluation System uses Google's Gemini 1.5 Pro model to perform
        comprehensive usability evaluations of UI elements based on Nielsen's 10 Usability Heuristics.
        This system combines advanced AI capabilities with established UX evaluation methodologies to
        provide detailed, actionable insights for interface improvements."""
        self.story.append(Paragraph(purpose_text, self.body_style))
        self.story.append(Spacer(1, 0.1*inch))

        # Key Features
        self.story.append(Paragraph("Key Features", self.subsection_style))
        features = [
            "Multimodal Analysis: Combines visual screenshots with technical JSON data",
            "Section-wise Evaluation: Hierarchical analysis of UI sections and child elements",
            "Vision-Enhanced Assessment: Leverages Gemini Vision API for visual context",
            "Structured Output: Returns standardized evaluation results in JSON format",
            "Real-time Processing: Immediate evaluation results with comprehensive reporting"
        ]

        for feature in features:
            self.story.append(Paragraph(f"• {feature}", self.list_style))

        self.story.append(Spacer(1, 0.1*inch))

        # Evaluation Principles
        self.story.append(Paragraph("Nielsen's 10 Usability Heuristics", self.subsection_style))
        heuristics = [
            "1. Visibility of System Status",
            "2. Match Between System and Real World",
            "3. User Control and Freedom",
            "4. Consistency and Standards",
            "5. Error Prevention",
            "6. Recognition Rather Than Recall",
            "7. Flexibility and Efficiency of Use",
            "8. Aesthetic and Minimalist Design",
            "9. Help Users Recognize, Diagnose, and Recover from Errors",
            "10. Help and Documentation"
        ]

        for heuristic in heuristics:
            self.story.append(Paragraph(f"• {heuristic}", self.list_style))

    def add_data_types_section(self):
        """Add data types section"""
        self.story.append(PageBreak())
        self.story.append(Paragraph("2. Data Types Provided to Gemini", self.section_style))

        # Visual Data
        self.story.append(Paragraph("2.1 Visual Data (Images)", self.subsection_style))
        visual_text = """The system provides two types of visual data to the Gemini model:"""
        self.story.append(Paragraph(visual_text, self.body_style))

        visual_components = [
            "Full Screenshot: Complete interface with target element highlighted in red",
            "Element Close-up: Cropped view of the specific UI element being evaluated"
        ]

        for component in visual_components:
            self.story.append(Paragraph(f"• {component}", self.list_style))

        # Technical specifications
        tech_specs = """
Technical Specifications:
• Format: PNG
• Encoding: Base64
• Maximum Size: 1024x1024 pixels
• Quality: High (configurable)
• Compression: LANCZOS resampling for resizing
        """
        self.story.append(Paragraph(tech_specs, self.code_style))

        # JSON Data
        self.story.append(Paragraph("2.2 Structured JSON Data", self.subsection_style))
        json_text = """Comprehensive element information provided in structured JSON format includes:"""
        self.story.append(Paragraph(json_text, self.body_style))

        json_components = [
            "Element coordinates (x, y, width, height)",
            "HTML tag information and text content",
            "CSS selectors and XPath identifiers",
            "Computed styles and HTML attributes",
            "Contextual relationships (parent sections, child elements)"
        ]

        for component in json_components:
            self.story.append(Paragraph(f"• {component}", self.list_style))

        # Text Prompts
        self.story.append(Paragraph("2.3 Text Prompts", self.subsection_style))
        prompt_text = """Structured natural language instructions that include:"""
        self.story.append(Paragraph(prompt_text, self.body_style))

        prompt_components = [
            "Nielsen's 10 Usability Heuristics with detailed descriptions",
            "Context-specific evaluation instructions",
            "Visual analysis guidance (when images are provided)",
            "Expected response format specifications"
        ]

        for component in prompt_components:
            self.story.append(Paragraph(f"• {component}", self.list_style))

    def add_configuration_section(self):
        """Add configuration settings section"""
        self.story.append(PageBreak())
        self.story.append(Paragraph("3. Configuration Settings", self.section_style))

        # Model Configuration
        self.story.append(Paragraph("3.1 Model Configuration", self.subsection_style))
        model_config = """
MODEL_NAME = "gemini-1.5-pro-latest"  # Vision-capable model
TEMPERATURE = 0.7                     # Response creativity level
MAX_TOKENS = 10000                    # Maximum response length
        """
        self.story.append(Paragraph(model_config, self.code_style))

        # Vision Configuration
        self.story.append(Paragraph("3.2 Vision Configuration", self.subsection_style))
        vision_config = """
ENABLE_VISION = True                  # Enable/disable visual analysis
IMAGE_QUALITY = "high"                # Image quality: high/medium/low
MAX_IMAGE_SIZE = (1024, 1024)         # Maximum image dimensions
        """
        self.story.append(Paragraph(vision_config, self.code_style))

        # File Paths
        self.story.append(Paragraph("3.3 File Paths", self.subsection_style))
        file_config = """
DEFAULT_COORDINATES_PATH = "coordinates.json"
DEFAULT_ELEMENT_INFO_PATH = "element_info.json"
        """
        self.story.append(Paragraph(file_config, self.code_style))

    def add_visual_processing_section(self):
        """Add visual data processing section"""
        self.story.append(PageBreak())
        self.story.append(Paragraph("4. Visual Data Processing", self.section_style))

        # Image Preparation Pipeline
        self.story.append(Paragraph("4.1 Image Preparation Pipeline", self.subsection_style))
        pipeline_text = """The visual data processing follows a systematic pipeline:"""
        self.story.append(Paragraph(pipeline_text, self.body_style))

        pipeline_steps = [
            "Screenshot Loading: Load base screenshot image",
            "Element Highlighting: Draw red rectangle around target element",
            "Element Cropping: Extract close-up view of the element",
            "Image Resizing: Resize to fit Gemini's size constraints",
            "Format Conversion: Convert to PNG format",
            "Base64 Encoding: Encode for API transmission"
        ]

        for step in pipeline_steps:
            self.story.append(Paragraph(f"• {step}", self.list_style))

        # Visual Analysis Features
        self.story.append(Paragraph("4.2 Visual Analysis Features", self.subsection_style))
        features = [
            "Highlighted Elements: Red rectangles for clear identification",
            "Context Preservation: Full interface view for spatial understanding",
            "Detail Enhancement: Close-up views for precise analysis",
            "Quality Optimization: Automatic resizing and compression"
        ]

        for feature in features:
            self.story.append(Paragraph(f"• {feature}", self.list_style))

        # Image Data Structure
        self.story.append(Paragraph("4.3 Image Data Structure", self.subsection_style))
        image_structure = """
images_data = {
    "has_images": True,
    "full_screenshot": {
        "image": highlighted_image,
        "description": "Full screenshot with highlighted element"
    },
    "element_closeup": {
        "image": cropped_image,
        "description": "Detailed view of the specific element"
    }
}
        """
        self.story.append(Paragraph(image_structure, self.code_style))

    def add_json_structure_section(self):
        """Add JSON data structure section"""
        self.story.append(PageBreak())
        self.story.append(Paragraph("5. JSON Data Structure", self.section_style))

        # Basic Element Information
        self.story.append(Paragraph("5.1 Basic Element Information", self.subsection_style))
        basic_structure = """
element_info = {
    "index": int,                    # Element index in dataset
    "label": str,                    # Human-readable element label
    "coordinates": {                 # Element position and size
        "x": int,
        "y": int,
        "width": int,
        "height": int
    },
    "tag": str,                      # HTML tag name
    "text": str,                     # Text content
    "css_selector": str,             # CSS selector
    "xpath": str,                    # XPath selector
    "computed_style": dict,          # CSS computed styles
    "attributes": dict               # HTML attributes
}
        """
        self.story.append(Paragraph(basic_structure, self.code_style))

        # Enhanced Context Information
        self.story.append(Paragraph("5.2 Enhanced Context Information", self.subsection_style))
        enhanced_structure = """
enhanced_element_info = {
    # Basic element info (as above)
    "parent_section_context": {      # Section relationship data
        "section_label": str,
        "section_coordinates": dict,
        "section_key": str
    },
    "child_elements": list,          # Child element information
    "is_child_element": bool,        # Hierarchy flag
    "has_visual_context": bool,      # Visual data availability
    "evaluation_level": int          # Evaluation hierarchy level
}
        """
        self.story.append(Paragraph(enhanced_structure, self.code_style))

    def add_prompt_engineering_section(self):
        """Add prompt engineering section"""
        self.story.append(PageBreak())
        self.story.append(Paragraph("6. Prompt Engineering", self.section_style))

        # Base Evaluation Prompt
        self.story.append(Paragraph("6.1 Base Evaluation Prompt Structure", self.subsection_style))
        base_prompt = """
You are a UX expert conducting a comprehensive heuristic evaluation of a UI element.

HEURISTIC EVALUATION PRINCIPLES:
[Nielsen's 10 Usability Heuristics - detailed descriptions]

ELEMENT TO EVALUATE:
[JSON element data]

EVALUATION TASK:
Analyze this UI element against ALL 10 Nielsen's usability heuristics...
        """
        self.story.append(Paragraph(base_prompt, self.code_style))

        # Visual-Enhanced Prompt
        self.story.append(Paragraph("6.2 Visual-Enhanced Prompt Structure", self.subsection_style))
        visual_prompt = """
🖼️ VISUAL CONTEXT: You have been provided with actual screenshots showing this UI element.

📸 IMAGE ANALYSIS INSTRUCTIONS:
- The first image shows the full interface with the element highlighted in red
- The second image shows a close-up view of the specific element
- Analyze visual hierarchy, contrast, spacing, typography, colors
- Consider accessibility aspects (contrast ratios, text size)
        """
        self.story.append(Paragraph(visual_prompt, self.code_style))

        # Section-wise Evaluation Prompts
        self.story.append(Paragraph("6.3 Section-wise Evaluation Prompts", self.subsection_style))
        section_text = """The system uses specialized prompts for different evaluation levels:"""
        self.story.append(Paragraph(section_text, self.body_style))

        section_types = [
            "Level 0 (Section): Organizational unit evaluation",
            "Level 1 (Immediate Children): Individual functional element evaluation",
            "Context-aware: Includes parent-child relationship information"
        ]

        for section_type in section_types:
            self.story.append(Paragraph(f"• {section_type}", self.list_style))

    def add_multimodal_integration_section(self):
        """Add multimodal content integration section"""
        self.story.append(PageBreak())
        self.story.append(Paragraph("7. Multimodal Content Integration", self.section_style))

        # Content Assembly Process
        self.story.append(Paragraph("7.1 Content Assembly Process", self.subsection_style))
        assembly_text = """The multimodal content integration follows these steps:"""
        self.story.append(Paragraph(assembly_text, self.body_style))

        assembly_steps = [
            "Text Prompt Creation: Generate context-specific evaluation instructions",
            "Image Preparation: Encode visual data for API transmission",
            "Content Combination: Assemble multimodal content array",
            "API Transmission: Send combined content to Gemini Vision API"
        ]

        for step in assembly_steps:
            self.story.append(Paragraph(f"• {step}", self.list_style))

        # Content Parts Structure
        self.story.append(Paragraph("7.2 Content Parts Structure", self.subsection_style))
        content_structure = """
content_parts = [
    prompt_text,                                    # Evaluation instructions
    images_data['full_screenshot']['image'],        # Full interface image
    "📸 FULL SCREENSHOT: Complete interface...",    # Image description
    images_data['element_closeup']['image'],        # Element close-up
    "🔍 ELEMENT CLOSE-UP: Detailed view..."        # Image description
]
        """
        self.story.append(Paragraph(content_structure, self.code_style))

        # Fallback Mechanisms
        self.story.append(Paragraph("7.3 Fallback Mechanisms", self.subsection_style))
        fallback_text = """The system includes robust fallback mechanisms:"""
        self.story.append(Paragraph(fallback_text, self.body_style))

        fallback_types = [
            "Vision Disabled: Text-only evaluation mode",
            "Image Processing Errors: Automatic fallback to text analysis",
            "API Failures: Structured error responses with diagnostic information"
        ]

        for fallback in fallback_types:
            self.story.append(Paragraph(f"• {fallback}", self.list_style))

    def add_response_processing_section(self):
        """Add response processing section"""
        self.story.append(PageBreak())
        self.story.append(Paragraph("8. Response Processing", self.section_style))

        # Expected Response Format
        self.story.append(Paragraph("8.1 Expected Response Format", self.subsection_style))
        response_format = """
{
    "violations": [
        {
            "heuristic": "Heuristic name",
            "severity": "high/medium/low",
            "description": "Detailed violation description",
            "recommendation": "Specific improvement suggestion"
        }
    ],
    "passed_checks": [
        {
            "heuristic": "Heuristic name",
            "reason": "Why this heuristic is satisfied"
        }
    ],
    "overall_score": 85,
    "key_recommendations": ["Priority improvements"],
    "summary": "Overall evaluation summary"
}
        """
        self.story.append(Paragraph(response_format, self.code_style))

        # Response Parsing Pipeline
        self.story.append(Paragraph("8.2 Response Parsing Pipeline", self.subsection_style))
        parsing_text = """The response processing follows a systematic pipeline:"""
        self.story.append(Paragraph(parsing_text, self.body_style))

        parsing_steps = [
            "JSON Extraction: Remove markdown formatting and extract JSON",
            "Structure Validation: Verify response format compliance",
            "Data Mapping: Map Gemini response to internal format",
            "Error Handling: Fallback parsing for non-JSON responses",
            "Result Enhancement: Add metadata and evaluation flags"
        ]

        for step in parsing_steps:
            self.story.append(Paragraph(f"• {step}", self.list_style))

    def add_technical_implementation_section(self):
        """Add technical implementation details section"""
        self.story.append(PageBreak())
        self.story.append(Paragraph("9. Technical Implementation Details", self.section_style))

        # API Integration
        self.story.append(Paragraph("9.1 API Integration", self.subsection_style))
        api_details = [
            "Authentication: Google API key configuration",
            "Model Selection: Gemini 1.5 Pro (vision-capable)",
            "Rate Limiting: Built-in error handling and retries",
            "Content Limits: Automatic image resizing and token management"
        ]

        for detail in api_details:
            self.story.append(Paragraph(f"• {detail}", self.list_style))

        # Error Handling
        self.story.append(Paragraph("9.2 Error Handling", self.subsection_style))
        error_handling = [
            "API Failures: Graceful degradation with fallback responses",
            "Image Processing Errors: Automatic text-only mode",
            "JSON Parsing Errors: Fallback text parsing mechanisms",
            "Configuration Issues: Comprehensive error reporting"
        ]

        for error in error_handling:
            self.story.append(Paragraph(f"• {error}", self.list_style))

        # Performance Optimizations
        self.story.append(Paragraph("9.3 Performance Optimizations", self.subsection_style))
        optimizations = [
            "Image Compression: Automatic resizing for API efficiency",
            "Batch Processing: Support for multiple element evaluation",
            "Caching: Visual data preparation optimization",
            "Memory Management: Efficient image handling and cleanup"
        ]

        for optimization in optimizations:
            self.story.append(Paragraph(f"• {optimization}", self.list_style))

    def add_conclusion_section(self):
        """Add conclusion section"""
        self.story.append(PageBreak())
        self.story.append(Paragraph("10. Conclusion", self.section_style))

        conclusion_text = """
This documentation provides a comprehensive overview of how data is structured, processed, and
provided to the Gemini model in the Heuristic Evaluation System. The system's multimodal approach,
combining visual screenshots with detailed technical data, enables thorough and accurate usability
evaluations that leverage both AI vision capabilities and structured analysis methodologies.

The integration of Nielsen's 10 Usability Heuristics with Gemini's advanced AI capabilities creates
a powerful tool for automated UI evaluation. The system's ability to process both visual and
technical data ensures comprehensive analysis that considers both aesthetic and functional aspects
of user interface design.

Key benefits of this approach include:
• Enhanced accuracy through multimodal analysis
• Consistent evaluation criteria based on established UX principles
• Scalable automated assessment for large-scale UI evaluation
• Detailed, actionable feedback for interface improvements
• Support for hierarchical evaluation of complex UI structures

This system represents a significant advancement in automated usability evaluation, providing
UX professionals with a powerful tool for comprehensive interface assessment.
        """
        self.story.append(Paragraph(conclusion_text, self.body_style))

    def generate_pdf(self):
        """Generate the complete PDF document"""
        print("🔄 Generating PDF documentation...")

        # Add all sections
        self.add_title_page()
        self.add_table_of_contents()
        self.add_system_overview()
        self.add_data_types_section()
        self.add_configuration_section()
        self.add_visual_processing_section()
        self.add_json_structure_section()
        self.add_prompt_engineering_section()
        self.add_multimodal_integration_section()
        self.add_response_processing_section()
        self.add_technical_implementation_section()
        self.add_conclusion_section()

        # Build the PDF
        self.doc.build(self.story)
        print(f"✅ PDF documentation generated: {self.filename}")
        return self.filename

def main():
    """Main function to generate the PDF documentation"""
    try:
        generator = PDFDocumentGenerator()
        pdf_file = generator.generate_pdf()

        # Check if file was created successfully
        if os.path.exists(pdf_file):
            file_size = os.path.getsize(pdf_file)
            print(f"📄 PDF file created successfully!")
            print(f"📁 File: {pdf_file}")
            print(f"📏 Size: {file_size:,} bytes")
        else:
            print("❌ Error: PDF file was not created")

    except Exception as e:
        print(f"❌ Error generating PDF: {str(e)}")
        print("💡 Make sure you have reportlab installed: pip install reportlab")

if __name__ == "__main__":
    main()
