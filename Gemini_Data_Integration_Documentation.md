# Gemini Model Data Integration Documentation
## Heuristic Evaluation System - Complete Technical Specification

---

## Table of Contents

1. [System Overview](#system-overview)
2. [Data Types Provided to Gemini](#data-types-provided-to-gemini)
3. [Configuration Settings](#configuration-settings)
4. [Visual Data Processing](#visual-data-processing)
5. [JSON Data Structure](#json-data-structure)
6. [Prompt Engineering](#prompt-engineering)
7. [Multimodal Content Integration](#multimodal-content-integration)
8. [Response Processing](#response-processing)
9. [Evaluation Workflows](#evaluation-workflows)
10. [Technical Implementation Details](#technical-implementation-details)

---

## 1. System Overview

### Purpose
The Heuristic Evaluation System uses Google's Gemini 1.5 Pro model to perform comprehensive usability evaluations of UI elements based on Nielsen's 10 Usability Heuristics.

### Key Features
- **Multimodal Analysis**: Combines visual screenshots with technical JSON data
- **Section-wise Evaluation**: Hierarchical analysis of UI sections and child elements
- **Vision-Enhanced Assessment**: Leverages Gemini Vision API for visual context
- **Structured Output**: Returns standardized evaluation results in JSON format

### Evaluation Principles
The system evaluates UI elements against Nielsen's 10 Usability Heuristics:
1. Visibility of System Status
2. Match Between System and Real World
3. User Control and Freedom
4. Consistency and Standards
5. Error Prevention
6. Recognition Rather Than Recall
7. Flexibility and Efficiency of Use
8. Aesthetic and Minimalist Design
9. Help Users Recognize, Diagnose, and Recover from Errors
10. Help and Documentation

---

## 2. Data Types Provided to Gemini

### 2.1 Visual Data (Images)
**Type**: Base64-encoded PNG images
**Components**:
- **Full Screenshot**: Complete interface with target element highlighted in red
- **Element Close-up**: Cropped view of the specific UI element being evaluated

**Technical Specifications**:
- Format: PNG
- Encoding: Base64
- Maximum Size: 1024x1024 pixels
- Quality: High (configurable)
- Compression: LANCZOS resampling for resizing

### 2.2 Structured JSON Data
**Type**: Comprehensive element information in JSON format
**Components**:
- Element coordinates (x, y, width, height)
- HTML tag information
- Text content
- CSS selectors and XPath
- Computed styles
- HTML attributes
- Contextual relationships (parent sections, child elements)

### 2.3 Text Prompts
**Type**: Structured natural language instructions
**Components**:
- Nielsen's 10 Usability Heuristics (detailed descriptions)
- Evaluation instructions
- Context-specific guidance
- Visual analysis instructions (when images are provided)
- Expected response format specifications

---

## 3. Configuration Settings

### 3.1 Model Configuration
```python
MODEL_NAME = "gemini-1.5-pro-latest"  # Vision-capable model
TEMPERATURE = 0.7                     # Response creativity level
MAX_TOKENS = 10000                    # Maximum response length
```

### 3.2 Vision Configuration
```python
ENABLE_VISION = True                  # Enable/disable visual analysis
IMAGE_QUALITY = "high"                # Image quality: high/medium/low
MAX_IMAGE_SIZE = (1024, 1024)         # Maximum image dimensions
```

### 3.3 File Paths
```python
DEFAULT_COORDINATES_PATH = "coordinates.json"
DEFAULT_ELEMENT_INFO_PATH = "element_info.json"
```

---

## 4. Visual Data Processing

### 4.1 Image Preparation Pipeline
1. **Screenshot Loading**: Load base screenshot image
2. **Element Highlighting**: Draw red rectangle around target element
3. **Element Cropping**: Extract close-up view of the element
4. **Image Resizing**: Resize to fit Gemini's size constraints
5. **Format Conversion**: Convert to PNG format
6. **Base64 Encoding**: Encode for API transmission

### 4.2 Visual Analysis Features
- **Highlighted Elements**: Red rectangles for clear identification
- **Context Preservation**: Full interface view for spatial understanding
- **Detail Enhancement**: Close-up views for precise analysis
- **Quality Optimization**: Automatic resizing and compression

### 4.3 Image Data Structure
```python
images_data = {
    "has_images": True,
    "full_screenshot": {
        "image": highlighted_image,
        "description": "Full screenshot with highlighted element"
    },
    "element_closeup": {
        "image": cropped_image,
        "description": "Detailed view of the specific element"
    }
}
```

---

## 5. JSON Data Structure

### 5.1 Basic Element Information
```python
element_info = {
    "index": int,                    # Element index in dataset
    "label": str,                    # Human-readable element label
    "coordinates": {                 # Element position and size
        "x": int,
        "y": int,
        "width": int,
        "height": int
    },
    "tag": str,                      # HTML tag name
    "text": str,                     # Text content
    "css_selector": str,             # CSS selector
    "xpath": str,                    # XPath selector
    "computed_style": dict,          # CSS computed styles
    "attributes": dict               # HTML attributes
}
```

### 5.2 Enhanced Context Information
```python
enhanced_element_info = {
    # Basic element info (as above)
    "parent_section_context": {      # Section relationship data
        "section_label": str,
        "section_coordinates": dict,
        "section_key": str
    },
    "child_elements": list,          # Child element information
    "is_child_element": bool,        # Hierarchy flag
    "has_visual_context": bool,      # Visual data availability
    "evaluation_level": int          # Evaluation hierarchy level
}
```

---

## 6. Prompt Engineering

### 6.1 Base Evaluation Prompt Structure
```
You are a UX expert conducting a comprehensive heuristic evaluation of a UI element.

HEURISTIC EVALUATION PRINCIPLES:
[Nielsen's 10 Usability Heuristics - detailed descriptions]

ELEMENT TO EVALUATE:
[JSON element data]

EVALUATION TASK:
Analyze this UI element against ALL 10 Nielsen's usability heuristics...
```

### 6.2 Visual-Enhanced Prompt Structure
```
🖼️ VISUAL CONTEXT: You have been provided with actual screenshots showing this UI element.

📸 IMAGE ANALYSIS INSTRUCTIONS:
- The first image shows the full interface with the element highlighted in red
- The second image shows a close-up view of the specific element
- Analyze visual hierarchy, contrast, spacing, typography, colors
- Consider accessibility aspects (contrast ratios, text size)
```

### 6.3 Section-wise Evaluation Prompts
- **Level 0 (Section)**: Organizational unit evaluation
- **Level 1 (Immediate Children)**: Individual functional element evaluation
- **Context-aware**: Includes parent-child relationship information

---

## 7. Multimodal Content Integration

### 7.1 Content Assembly Process
1. **Text Prompt Creation**: Generate context-specific evaluation instructions
2. **Image Preparation**: Encode visual data for API transmission
3. **Content Combination**: Assemble multimodal content array
4. **API Transmission**: Send combined content to Gemini Vision API

### 7.2 Content Parts Structure
```python
content_parts = [
    prompt_text,                                    # Evaluation instructions
    images_data['full_screenshot']['image'],        # Full interface image
    "📸 FULL SCREENSHOT: Complete interface...",    # Image description
    images_data['element_closeup']['image'],        # Element close-up
    "🔍 ELEMENT CLOSE-UP: Detailed view..."        # Image description
]
```

### 7.3 Fallback Mechanisms
- **Vision Disabled**: Text-only evaluation mode
- **Image Processing Errors**: Automatic fallback to text analysis
- **API Failures**: Structured error responses with diagnostic information

---

## 8. Response Processing

### 8.1 Expected Response Format
```json
{
    "violations": [
        {
            "heuristic": "Heuristic name",
            "severity": "high/medium/low",
            "description": "Detailed violation description",
            "recommendation": "Specific improvement suggestion"
        }
    ],
    "passed_checks": [
        {
            "heuristic": "Heuristic name",
            "reason": "Why this heuristic is satisfied"
        }
    ],
    "overall_score": 85,
    "key_recommendations": ["Priority improvements"],
    "summary": "Overall evaluation summary"
}
```

### 8.2 Response Parsing Pipeline
1. **JSON Extraction**: Remove markdown formatting and extract JSON
2. **Structure Validation**: Verify response format compliance
3. **Data Mapping**: Map Gemini response to internal format
4. **Error Handling**: Fallback parsing for non-JSON responses
5. **Result Enhancement**: Add metadata and evaluation flags

### 8.3 Parsed Result Structure
```python
evaluation_result = {
    "element_info": dict,              # Original element data
    "violations": list,                # Identified usability issues
    "passed_checks": list,             # Satisfied heuristics
    "overall_score": int,              # Numerical score (0-100)
    "recommendations": list,           # Improvement suggestions
    "gemini_analysis": str,            # AI-generated summary
    "evaluation_status": str,          # Success/error status
    "has_visual_context": bool,        # Visual data flag
    "vision_enabled": bool,            # Configuration flag
    "images_sent_to_gemini": bool      # Actual image transmission flag
}
```

---

## 9. Evaluation Workflows

### 9.1 Standard Element Evaluation
1. **Data Preparation**: Assemble element information
2. **Prompt Generation**: Create evaluation instructions
3. **API Call**: Send data to Gemini model
4. **Response Processing**: Parse and structure results
5. **Result Enhancement**: Add metadata and flags

### 9.2 Visual-Enhanced Evaluation
1. **Screenshot Processing**: Prepare visual data
2. **Multimodal Assembly**: Combine text and images
3. **Vision API Call**: Send multimodal content
4. **Enhanced Parsing**: Process vision-aware responses
5. **Visual Metadata**: Add visual analysis flags

### 9.3 Section-wise Evaluation
1. **Hierarchy Analysis**: Identify sections and children
2. **Context Building**: Assemble relationship data
3. **Level-specific Prompts**: Generate appropriate instructions
4. **Coordinated Evaluation**: Process sections and children
5. **Hierarchical Results**: Structure section-based outputs

---

## 10. Technical Implementation Details

### 10.1 API Integration
- **Authentication**: Google API key configuration
- **Model Selection**: Gemini 1.5 Pro (vision-capable)
- **Rate Limiting**: Built-in error handling and retries
- **Content Limits**: Automatic image resizing and token management

### 10.2 Error Handling
- **API Failures**: Graceful degradation with fallback responses
- **Image Processing Errors**: Automatic text-only mode
- **JSON Parsing Errors**: Fallback text parsing mechanisms
- **Configuration Issues**: Comprehensive error reporting

### 10.3 Performance Optimizations
- **Image Compression**: Automatic resizing for API efficiency
- **Batch Processing**: Support for multiple element evaluation
- **Caching**: Visual data preparation optimization
- **Memory Management**: Efficient image handling and cleanup

### 10.4 Quality Assurance
- **Response Validation**: Structured output verification
- **Consistency Checks**: Cross-evaluation validation
- **Metadata Tracking**: Comprehensive evaluation audit trail
- **Debug Information**: Detailed logging and error reporting

---

## Conclusion

This documentation provides a comprehensive overview of how data is structured, processed, and provided to the Gemini model in the Heuristic Evaluation System. The system's multimodal approach, combining visual screenshots with detailed technical data, enables thorough and accurate usability evaluations that leverage both AI vision capabilities and structured analysis methodologies.
